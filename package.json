{"type": "module", "version": "4.14.2", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"dev": "tsx ./serve.ts", "build": "tsx ./build.ts --mode production", "tst": "tsx ./build.ts --mode test", "test": "tsx ./build.ts --mode test", "build:test": "npx tst", "serve": "http-server ./dist -o", "serve:test": "http-server ./dist-test -o", "svgo": "svgo public/assets/**/*.svg", "new": "plop", "generate:icons": "esno ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc -b", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "postinstall": "simple-git-hooks", "preinstall": "npx only-allow pnpm", "commit": "git cz", "release": "bumpp"}, "dependencies": {"@admin/components": "workspace:*", "@admin/utils": "workspace:*", "@headlessui/vue": "^1.7.23", "@imengyu/vue3-context-menu": "^1.4.4", "@shencom/api": "^1.9.5", "@shencom/request": "^1.5.1", "@shencom/utils": "^1.15.2", "@taptap/tds-msg": "git+https://gitlab.shencom.cn/web/tools/tds-msg-kit.git#v0.3.2", "@vueuse/components": "^12.0.0", "@vueuse/core": "^12.0.0", "@vueuse/integrations": "^12.0.0", "axios": "^1.7.9", "dayjs": "^1.11.13", "defu": "^6.1.4", "disable-devtool": "^0.3.8", "element-plus": "^2.9.0", "eruda": "^3.4.1", "floating-vue": "5.2.2", "hotkeys-js": "^3.13.7", "lodash-es": "^4.17.21", "medium-zoom": "^1.1.0", "mitt": "^3.0.1", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "overlayscrollbars": "^2.10.1", "overlayscrollbars-vue": "^0.5.9", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^2.3.0", "pinyin-pro": "^3.26.0", "qs": "^6.13.1", "scule": "^1.3.0", "sortablejs": "^1.15.6", "spinkit": "^2.0.1", "timeago.js": "^4.0.2", "v-wave": "^3.0.2", "vconsole": "^3.15.1", "vue": "^3.5.13", "vue-i18n": "^10.0.5", "vue-m-message": "^4.0.2", "vue-router": "^4.5.0", "watermark-js-plus": "^1.5.7"}, "devDependencies": {"@antfu/eslint-config": "3.11.2", "@iconify/json": "^2.2.280", "@iconify/vue": "^4.2.0", "@intlify/unplugin-vue-i18n": "^6.0.1", "@shencom/cli": "^1.4.0", "@shencom/npmlog": "^1.1.1", "@shencom/oss-upload": "^2.7.1", "@shencom/typing": "^0.4.0", "@stylistic/stylelint-config": "^2.0.0", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.17", "@types/sortablejs": "^1.15.8", "@unocss/eslint-plugin": "^0.65.1", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "autoprefixer": "^10.4.20", "boxen": "^8.0.1", "bumpp": "^9.9.0", "cz-git": "^1.11.0", "eslint": "^9.16.0", "esno": "^4.8.0", "fs-extra": "^11.2.0", "http-server": "^14.1.1", "inquirer": "^12.2.0", "lint-staged": "^15.2.10", "npm-run-all2": "^7.0.1", "picocolors": "^1.1.1", "plop": "^4.0.1", "postcss": "^8.4.49", "postcss-nested": "^7.0.2", "sass-embedded": "^1.82.0", "simple-git-hooks": "^2.11.1", "stylelint": "^16.11.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.10.0", "svgo": "^3.3.2", "typescript": "^5.6.3", "unocss": "^0.63.4", "unocss-preset-animations": "^1.1.0", "unocss-preset-scrollbar": "^0.3.1", "unplugin-auto-import": "^0.18.6", "unplugin-turbo-console": "^1.10.6", "unplugin-vue-components": "^0.27.5", "vite": "^6.0.3", "vite-plugin-app-loading": "^0.3.0", "vite-plugin-archiver": "^0.1.1", "vite-plugin-banner": "^0.8.0", "vite-plugin-compression2": "^1.3.3", "vite-plugin-fake-server": "^2.1.4", "vite-plugin-pages": "^0.32.4", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.6.7", "vite-plugin-vue-meta-layouts": "^0.5.1", "vue-tsc": "^2.1.10"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "preserveUnused": true}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}