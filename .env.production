# 应用配置面板
VITE_APP_SETTING = false
# 页面标题
VITE_APP_TITLE = 深安先锋
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL =
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =
# 是否禁用开发者工具，可防止被调试
VITE_APP_DISABLE_DEVTOOL = false

# 是否在打包时启用 Mock
VITE_BUILD_MOCK = false
# 是否在打包时生成 sourcemap
VITE_BUILD_SOURCEMAP = true
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS =
# 是否在打包后生成存档，支持 zip 和 tar
VITE_BUILD_ARCHIVE =

# 环境
VITE_APP_ENV = production
# 应用 SCID
VITE_APP_SCID = sc54e9870bdea9e80f
# OSS 代码路径
VITE_OSS_CODE_PATH = plugins/scloud/admin/${VITE_APP_PROJECT_NAME}/
# OSS 项目CND
VITE_OSS_PROJECT_CND = ${VITE_OSS_HOST}/${VITE_OSS_CODE_PATH}
# 是否开启30天免登录
VITE_APP_30_DAY_LOGIN_ENABLE = true
