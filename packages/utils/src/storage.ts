import { ScStorageBase } from '@shencom/utils'
import { type RemovableRef, useStorage } from '@vueuse/core'
import { APP_SCID } from './env'

export const Storages = new ScStorageBase({
  scid: APP_SCID,
  get: window.localStorage.getItem.bind(window.localStorage),
  set: window.localStorage.setItem.bind(window.localStorage),
  remove: window.localStorage.removeItem.bind(window.localStorage),
  clear: window.localStorage.clear.bind(window.localStorage),
  keys: () => Object.keys(window.localStorage),
})

const UseStorageOptions = {
  serializer: {
    write: (v: any) => v,
    read: (v: any) => v,
  },
}

export function useStorageData<T = any>(k: string, val: T): RemovableRef<T> {
  return useStorage<T>(
    k,
    val,
    {
      getItem: Storages.getData.bind(Storages),
      setItem: Storages.setData.bind(Storages),
      removeItem: Storages.removeData.bind(Storages),
    },
    UseStorageOptions,
  )
}

export function useStorageUser<T>(k: string, val: T): RemovableRef<T> {
  return useStorage(
    k,
    val,
    {
      getItem: Storages.getUser.bind(Storages),
      setItem: Storages.setUser.bind(Storages),
      removeItem: Storages.removeUser.bind(Storages),
    },
    UseStorageOptions,
  )
}

export function useStorageLasting<T>(k: string, val: T): RemovableRef<T> {
  return useStorage(
    k,
    val,
    {
      getItem: Storages.getLasting.bind(Storages),
      setItem: Storages.setLasting.bind(Storages),
      removeItem: Storages.removeLasting.bind(Storages),
    },
    UseStorageOptions,
  )
}
