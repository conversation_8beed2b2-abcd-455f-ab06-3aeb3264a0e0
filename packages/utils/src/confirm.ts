import type { ElMessageBoxOptions } from 'element-plus'
import { ElMessageBox } from 'element-plus'

function ConfirmBase(...args: [message: string, options?: ElMessageBoxOptions]) {
  return new Promise<boolean>((resolve) => {
    const [message, options] = args
    ElMessageBox.confirm(message, {
      title: '提示',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      ...options,
    })
      .then(() => resolve(true))
      .catch(() => resolve(false))
  })
}

class Confirm {
  static warning(...args: Parameters<typeof ConfirmBase>) {
    const [message, options] = args
    return ConfirmBase(message, { ...options, type: 'warning' })
  }
}

export { Confirm }
