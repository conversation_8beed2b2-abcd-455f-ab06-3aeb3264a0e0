import type { AxiosError } from 'axios'
import Message from 'vue-m-message'
// import Toast from './toast';
// import { Sentry } from '@shencom/plugins';

function responseError(error: AxiosError) {
  console.log('请求错误--- :', error, '\n', { ...error })

  // if (!error || !error.statusCode) return;

  // const { config, request, response, message } = error;

  // const method = (config.method && config.method.toLocaleUpperCase()) || '';

  // const { url = '' } = config;
  // const { status } = request;
  // const data = error?.data || response?.data;
  // const body = config.params || JSON.parse(config.data || '{}');

  // Sentry.setRequest({
  //   url,
  //   data,
  //   body,
  //   status,
  //   method,
  //   message,
  //   header: config.headers || {},
  // });
}

function tryError(error: any) {
  console.log('脚本错误--- :', error, { ...error })

  // Sentry.captureException(error);
}

/**
 * 全局错误处理
 *
 * @param {(Error | HttpError)} error 错误信息
 * @param {boolean} [isHttp] 在请求拦截中传 `true` 为了防止请求错误重复上报
 * @return {*}
 */
export function ExceptionHandler(error: any, isHttp = false) {
  console.log('error :>> ', error)
  if (!error) {
    return
  }

  if (error.statusCode) {
    if (isHttp) {
      responseError(error)
    }
  }
  else {
    tryError(error)
  }
}

export function ExceptionToast(error: any, msg: string) {
  if (error) {
    const errmsg = error.data?.errmsg || error.data?.message || error.message || ''
    msg += ` ${errmsg || error.statusCode}`
  }
  Message.error(msg, {
    zIndex: 2000,
  })
  return msg
}
