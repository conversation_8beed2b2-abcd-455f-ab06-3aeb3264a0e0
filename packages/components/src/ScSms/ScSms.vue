<script setup lang="ts">
import type { Unfurl } from '@shencom/typing'
import { ApiSmsTenants } from '@shencom/api'
import { ValidatePhone } from '@shencom/utils-validate'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import Message from 'vue-m-message'

type ApiSmsTenantsReqBody = Unfurl<Parameters<typeof ApiSmsTenants>[0]>

export interface SmsProps {
  value: string
  title?: string
  type: 'phone' | 'username' | 'all'
  seconds?: number
  beforeSend?: (value: string) => boolean | Promise<boolean>
}

interface IEmits {
  (e: 'click', val: boolean): void
}

defineOptions({ name: 'ScSms' })

const props = withDefaults(defineProps<SmsProps>(), {
  title: '发送验证码',
  type: 'phone',
  seconds: 60,
})

const emits = defineEmits<IEmits>()

const SMS_TIME_KEY = 'data_SMS_TIME_KEY'
const millisecond = props.seconds * 1000

const countDown = ref(props.seconds)

const buttonText = ref(props.title)
const isLoading = ref(false)
const isSended = ref(false)
const timer = ref<ReturnType<typeof setInterval> | null>(null)

const formType = computed(() => {
  if (props.type === 'all') {
    if (props.value.length === 11 && Number(props.value)) {
      return 'phone'
    }
    else {
      return 'username'
    }
  }
  return props.type
})

onMounted(() => {
  if (import.meta.env.MODE === 'production') {
    getTime()
  }
})

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})

function getTime() {
  const old = JSON.parse(window.sessionStorage.getItem(SMS_TIME_KEY) || '0')

  const now = new Date().getTime()
  if (!old || now - +old > millisecond) {
    countDown.value = props.seconds
    window.sessionStorage.removeItem(SMS_TIME_KEY)
  }
  else {
    countDown.value = Math.floor((millisecond - (now - +old)) / 1000)
  }

  if (countDown.value !== props.seconds) {
    isSended.value = true
    interval()
  }
}

function interval() {
  if (timer.value && isSended.value) {
    return
  }
  isSended.value = true
  buttonText.value = `重新发送(${countDown.value--})`
  timer.value = setInterval(() => {
    if (countDown.value <= 0) {
      initSms()
      return
    }
    buttonText.value = `重新发送(${countDown.value--})`
  }, 1000)
}

function initSms() {
  isSended.value = false
  countDown.value = props.seconds
  buttonText.value = props.title
  if (timer.value) {
    clearInterval(timer.value)
  }
  timer.value = null
  window.sessionStorage.removeItem(SMS_TIME_KEY)
}

function onSendSms() {
  if (isSended.value) {
    return
  }

  const { value } = props

  if (formType.value === 'phone' && !ValidatePhone(value)) {
    Message.warning('手机号不正确')
    return
  }

  if (formType.value === 'username' && !value) {
    Message.warning('信息不能为空')
    return
  }

  onSubmit()
}

async function onSubmit() {
  const { beforeSend, value } = props

  if (beforeSend && !(await beforeSend(value))) {
    return
  }

  try {
    isLoading.value = true

    const body = {} as ApiSmsTenantsReqBody
    body[formType.value] = value
    await ApiSmsTenants(body)
    isLoading.value = false
    Message.success('短信已发送,请查收！')

    window.sessionStorage.setItem(SMS_TIME_KEY, new Date().getTime().toString())
    interval()
    emits('click', true)
  }
  catch (error: any) {
    isLoading.value = false
    const msg = (error.data && error.data.errmsg) || '发送失败，请稍后再试'
    Message.error(msg)

    initSms()
    emits('click', false)
  }
}
</script>

<template>
  <ElButton type="primary" :loading="isLoading" :disabled="isSended" @click="onSendSms">
    {{ buttonText }}
  </ElButton>
</template>
