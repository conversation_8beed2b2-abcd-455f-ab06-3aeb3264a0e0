import { ref } from 'vue'

export function useAgreement() {
  const { $utils } = useGlobal()

  const isAgreed = ref(true)
  const isAgreementVisible = ref(false)

  async function showTip() {
    return $utils.Confirm.warning(
      '请您认真阅读《用户服务协议及隐私保护政策》的全部内容，同意后可以开始使用我们的服务',
      {
        confirmButtonText: '阅读协议',
      },
    )
  }

  function setAgreementVisible(isShow: boolean) {
    isAgreementVisible.value = isShow
  }

  function onAgreeConfirm(result: boolean) {
    isAgreed.value = result
    setAgreementVisible(false)
  }

  function checkAgreement() {
    if (!isAgreed.value) {
      showTip().then((b) => {
        if (b) {
          setAgreementVisible(true)
        }
      })
    }
    return isAgreed.value
  }

  return { isAgreementVisible, isAgreed, checkAgreement, setAgreementVisible, onAgreeConfirm }
}
