import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  // {
  //   path: '/login',
  //   name: 'login',
  //   meta: { title: '登录', skipAuth: 'login', whiteList: true },
  //   component: () => import(/* webpackChunkName: "login-index" */ './pages/index.vue'),
  // },
  {
    path: '/login',
    name: 'login',
    meta: { title: '登录', skipAuth: 'login', whiteList: true },
    component: () => import(/* webpackChunkName: "login-index" */ './pages/index.vue'),
  },
]

export { routes }
