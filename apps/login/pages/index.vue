<script setup lang="ts">
import Copyright from '@/layouts/components/Copyright/index.vue'
import ColorScheme from '@/layouts/components/Topbar/Toolbar/ColorScheme/index.vue'

import useSettingsStore from '@/store/modules/settings'
import { useStorage } from '@vueuse/core'
import Form from './components/Form/index.vue'

interface IProps {
  modelValue?: boolean
  pageDescription?: string
  pageTitle?: string
}
interface IEmits {
  (e: 'update:modelValue', val: any): void
}

const props = defineProps<IProps>()

const emits = defineEmits<IEmits>()
const { $service } = useGlobal()

const logo = computed(() => $service.ServiceSite.darkLogo)

const appName = computed(() => $service.ServiceSite.title)

const sloganImage = computed(() => $service.ServiceSite.config('mng_config_background') || new URL('../assets/images/slogan.svg', import.meta.url).href)

const pageTitle = computed(() => props.pageTitle || $service.ServiceSite.title)

const layoutAlign = useStorage<'left' | 'center' | 'right'>('login_layout_align', 'right')

const settingsStore = useSettingsStore()
</script>

<template>
  <div class="min-h-full flex flex-1 select-none overflow-x-hidden">
    <div
      class="absolute right-4 top-4 z-1 flex-center rounded-3xl rounded-full bg-gray/30 p-1 px-4 text-base ring-1 ring-stone-2 dark-ring-stone-8"
    >
      <HDropdownMenu
        v-if="settingsStore.mode === 'pc'" :items="[[
          { label: '左侧布局', disabled: layoutAlign === 'left', handle: () => { layoutAlign = 'left' } },
          { label: '居中布局', disabled: layoutAlign === 'center', handle: () => { layoutAlign = 'center' } },
          { label: '右侧布局', disabled: layoutAlign === 'right', handle: () => { layoutAlign = 'right' } },
        ]]" class="cursor-pointer rounded-full p-2 hover-bg-[var(--g-bg)]"
      >
        <SvgIcon
          :name="{
            left: 'i-icon-park-outline:left-bar',
            center: 'i-icon-park-outline:square',
            right: 'i-icon-park-outline:right-bar',
          }[layoutAlign]"
        />
      </HDropdownMenu>
      <ColorScheme v-if="settingsStore.settings.toolbar.colorScheme" class="rounded-full hover-bg-[var(--g-bg)]" />
    </div>

    <div v-if="logo || appName" class="absolute left-0 top-0 z-10 flex flex-1">
      <div class="ml-4 mt-4 flex flex-1 items-center text-foreground sm:left-6 sm:top-6 lg:text-foreground">
        <img v-if="logo" :alt="appName" :src="logo" class="mr-2" width="42">
        <p v-if="appName" class="text-xl font-medium">
          {{ appName }}
        </p>
      </div>
    </div>

    <div class="w-full flex" :class="{ 'flex-jc': layoutAlign === 'center', 'flex-row-reverse': layoutAlign === 'left' }">
      <div
        v-show="layoutAlign !== 'center'"
        class="relative w-0 flex-1 hidden lg:block"
      >
        <div class="absolute inset-0 h-full w-full bg-background-deep dark:bg-[#070709]">
          <div class="login-background absolute left-0 top-0 size-full" />
          <div class="mr-20 h-full flex-col-center -enter-x">
            <template v-if="sloganImage">
              <img :alt="appName" :src="sloganImage" class="h-64 w-2/5 animate-float">
            </template>
            <SloganIcon v-else :alt="appName" class="h-64 w-2/5 translate-x-50px animate-float" />
            <div class="mt-6 text-xl text-foreground font-sans lg:text-2xl">
              {{ pageTitle }}
            </div>
            <div class="mt-2 dark:text-muted-foreground">
              {{ pageDescription }}
            </div>
          </div>
        </div>
      </div>

      <div
        :class="{ 'w-full': layoutAlign === 'center' }"
        class="relative min-h-full w-[34%] flex-col-center flex-1 px-6 py-10 lg:flex-initial lg:px-8"
      >
        <div v-show="layoutAlign === 'center'" class="login-background absolute left-0 top-0 size-full" />
        <div class="mx-auto mt-6 enter-x sm:mx-auto md:max-w-md">
          <div class="h-120 w-90">
            <Form />
          </div>
        </div>
        <div class="absolute bottom-3 flex text-center text-xs text-muted-foreground">
          <Copyright />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-background {
  background:
    linear-gradient(
      154deg,
      #07070915 30%,
      hsl(var(--primary) / 30%) 48%,
      #07070915 64%
    );
  filter: blur(100px);
}

.dark {
  .login-background {
    background:
      linear-gradient(
        154deg,
        #07070915 30%,
        hsl(var(--primary) / 20%) 48%,
        #07070915 64%
      );
    filter: blur(100px);
  }
}
</style>
