<script setup lang="ts">
import type { UnwrapRef } from 'vue'
import { ElLoading, type FormInstance, type FormRules } from 'element-plus'
import { computed, reactive, ref } from 'vue'
import Message from 'vue-m-message'
import { useAgreement } from '../../../hooks'
import { ServiceAccountLogin } from '../../../service'
import Agreement from '../Agreement.vue'
import AccountPassword from './AccountPassword.vue'
import MailComponent from './MailComponent.vue'
import RefreshCheckbox from './RefreshCheckbox.vue'

interface IEmits {
  (e: 'success', val: SC.User.RootInfo, type?: 'account'): void
  (e: 'toggle'): void
}

const emits = defineEmits<IEmits>()

const { $utils } = useGlobal()
const { isAgreed, isAgreementVisible, checkAgreement, setAgreementVisible, onAgreeConfirm }
  = useAgreement()

interface FormState {
  username: string
  password: string
  mailCode: string
}

const form: UnwrapRef<FormState> = reactive({
  username: $utils.IsDev ? '' : '',
  password: $utils.IsDev ? 'Sc123456' : '',
  mailCode: $utils.IsDev ? '000000' : '',
})

const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入手机号或用户名' },
    {
      validator: (rule, value, callback) => {
        if (!Number.isNaN(+value) && !$utils.ValidatePhone(value)) {
          callback('手机号格式错误')
        }
        callback()
      },
      trigger: 'blur',
    },
  ],
  password: [{ required: true, message: '请输入密码' }],
  mailCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码错误', trigger: 'blur' },
  ],
})

function validateUsername() {
  return new Promise<boolean>((resolve) => {
    if (!checkAgreement()) {
      resolve(false)
      return
    }
    ruleFormRef.value?.validateField('username', (isValid) => {
      resolve(isValid)
    })
  })
}

const disabledSubmit = computed(() => Object.values(form).some(v => !v))

const passwordVisible = ref(false)

function onSubmit() {
  if (!isAgreed.value) {
    checkAgreement()
    return
  }

  if (!ruleFormRef.value) {
    return
  }

  ruleFormRef.value.validate((valid) => {
    if (valid) {
      login()
    }
  })
}

async function login() {
  const loading = ElLoading.service({ text: '正在登录...', target: '.login-form' })
  try {
    const data = await ServiceAccountLogin({
      username: form.username,
      code: form.mailCode,
      password: form.password,
    })

    Message.success('登录成功!')
    emits('success', data, 'account')
  }
  catch (error: any) {
    Message.error((error.data && error.data.errmsg) || '登录失败')
  }
  finally {
    loading.close()
  }
}
</script>

<template>
  <div class="relative h-full flex-col p-4">
    <div class="sm:mx-auto sm:max-w-md sm:w-full">
      <h2 class="mb-3 text-3xl text-foreground font-bold leading-9 tracking-tight lg:text-4xl">
        欢迎回来 👋🏻
      </h2>
      <p class="lg:text-md text-sm text-muted-foreground">
        <span class="text-muted-foreground">请输入您的帐户信息</span>
      </p>
    </div>
    <div class="flex-ac flex-1">
      <el-form ref="ruleFormRef" class="w-full" status-icon size="large" :model="form" :rules="rules">
        <el-form-item prop="username">
          <el-input v-model="form.username" clearable placeholder="请输入手机号或用户名">
            <template #prefix>
              <i class="i-ic:baseline-person text-gray-400" />
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input v-model="form.password" clearable show-password type="password" placeholder="请输入密码">
            <template #prefix>
              <i class="i-ion:ios-lock text-gray-400" />
            </template>
          </el-input>
        </el-form-item>

        <el-form-item prop="mailCode">
          <MailComponent v-model="form.mailCode" :username="form.username" type="all" :before-send="validateUsername" />
        </el-form-item>

        <!-- <el-form-item prop="isAgreed">
          <div class="flex-ac text-sm">
            <el-checkbox v-model="isAgreed">
              <span class="mr-2 text-foreground">我已经阅读</span>
            </el-checkbox>
            <el-link type="primary" @click="setAgreementVisible(true)">
              《用户服务协议及隐私保护政策》
            </el-link>
          </div>
        </el-form-item> -->

        <el-button class="w-full" type="primary" size="large" :disabled="disabledSubmit" @click="onSubmit">
          登录
        </el-button>
      </el-form>
    </div>
    <div class="mb-2 flex-ac">
      <RefreshCheckbox />
    </div>
    <div class="flex justify-between">
      <el-button link @click="passwordVisible = true">
        <span class="flex-ac text-foreground underline transition-all duration-300 hover:text-primary">
          忘记密码？
        </span>
      </el-button>
      <span class="text-foreground/50">推荐使用二维码登录</span>
    </div>
  </div>

  <AccountPassword v-model="passwordVisible" />

  <!-- 隐私协议 -->
  <el-dialog v-model="isAgreementVisible" append-to-body align-center width="80%" title="用户服务条款及隐私保护政策">
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onAgreeConfirm(false)">不同意</el-button>
        <el-button type="primary" @click="onAgreeConfirm(true)"> 同意 </el-button>
      </span>
    </template>
    <Agreement />
  </el-dialog>
</template>
