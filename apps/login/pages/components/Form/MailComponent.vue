<script setup lang="ts">
import { ScSms } from '@admin/components'

interface IProps {
  username: string
  type: InstanceType<typeof ScSms>['type']
  beforeSend?: () => boolean | Promise<boolean>
}
interface IEmits {
  (e: 'submit'): void
}

defineOptions({ name: 'MailComponent' })

defineProps<IProps>()
const emits = defineEmits<IEmits>()

const mailCode = defineModel<string>('')
</script>

<template>
  <el-input v-model="mailCode" clearable placeholder="验证码" @keyup.enter="emits('submit')">
    <template #prefix>
      <el-icon size="20">
        <i class="i-ep:message text-gray-400" />
      </el-icon>
    </template>
    <template #append>
      <ScSms class="w-34" :value="username" type="username" :before-send="beforeSend" />
    </template>
  </el-input>
</template>
