<script setup lang="ts">
import { ApiWechatCodeLogin } from '@shencom/api'
import { UrlParam } from '@shencom/utils'
import { useToggle } from '@vueuse/core'
import { ElLoading } from 'element-plus'
import { onMounted, ref } from 'vue'
import Message from 'vue-m-message'
import { useRouter } from 'vue-router'
import { setUrlHref } from '../../../utils'
import Account from './Account.vue'
import Weixin from './Weixin.vue'

const { $utils, $service } = useGlobal()

const router = useRouter()
const [weixinLogin, toggleWeixinLogin] = useToggle($service.ServiceSite.config('login_method') === '2')

const weixinRef = ref<InstanceType<typeof Weixin>>()

const weixinLoginEnable = computed(() => $service.ServiceSite.config('scan_login_active') === '1')

onMounted(async () => {
  const code = UrlParam('code')
  if (code) {
    $utils.Storages.set('weixin_code', code, 5)
    onWeixinLogin(code)
  }
})

async function onWeixinLogin(code: string) {
  setUrlHref()

  const onClose = ElLoading.service({ text: '登录中...' })
  try {
    const { data } = await ApiWechatCodeLogin(code)
    if (typeof data === 'string') {
      Message.warning('该微信未绑定系统用户，请先绑定！')
      $utils.Storages.set('weixin_openId', data, 10)
      await nextTick()
      weixinRef.value?.onOpenWeixinBind()
    }
    else {
      Message.success('登录成功!')
      onSuccess(data.oAuth2AccessToken, 'weixin')
    }

    $utils.Storages.remove('weixin_code')
  }
  catch (error) {
    $utils.ExceptionHandler(error)
    $utils.ExceptionToast(error, '登录失败')

    const code = $utils.Storages.get('weixin_code')
    if (code) {
      $utils.Storages.remove('weixin_code')
      onWeixinLogin(code)
    }
  }
  finally {
    onClose.close()
  }
}

function onSuccess(data: SC.User.RootInfo, loginType?: string) {
  $utils.UserInfo.setRootInfo(data)

  // TODO: 首次登录是否需要修改密码
  // const hasNeedChangePwd = await this.handleJudgeNeedChangePwd(loginType);
  //   if (hasNeedChangePwd) {
  //    $utils.Storage.set('temporaryUserInfo', userInfo);
  //     return;
  //   }

  router.replace('/')
}
</script>

<template>
  <div class="login-form relative size-full flex-c rounded-3">
    <template v-if="weixinLoginEnable">
      <span class="qrcode absolute right-0 top-0 z-10 h-12 w-12 cursor-pointer text-foreground" @click="toggleWeixinLogin()">
        <i v-show="!weixinLogin" class="i-ant-design-qrcode-outlined size-full text-12" />
        <i v-show="weixinLogin" class="i-ant-design:laptop-outlined size-full text-12" />
      </span>
    </template>

    <div v-if="weixinLoginEnable" v-show="weixinLogin" class="size-full">
      <Weixin ref="weixinRef" v-model="weixinLogin" @success="onSuccess" />
    </div>
    <div v-show="!weixinLogin" class="size-full">
      <Account @success="onSuccess" />
    </div>
  </div>
</template>

<style scoped>
.qrcode {
  clip-path: polygon(0 10%, 100% 0, 90% 100%);
}
</style>
