<script setup lang="ts">
import { ref } from 'vue'

const { $service, $utils } = useInstance()

// 是否开启30天免登录
const enable = ref<boolean>($service.ServiceToken.enable)

const defaultValue = ref($service.ServiceToken.get30DayLogin() ?? !$utils.IsPro) // 正式服默认不勾选

watch(defaultValue, (val) => {
  $service.ServiceToken.set30DayLogin(Boolean(val))
})
</script>

<template>
  <el-checkbox v-if="enable" v-model="defaultValue">
    <span class="mr-2 text-foreground">30天内自动登录</span>
  </el-checkbox>
</template>
