<script setup lang="ts">
import { ServiceWechatGetQrcode } from '../../../service'
import { createIframe } from '../../../utils'
import RefreshCheckbox from './RefreshCheckbox.vue'
import WeixinBind from './WeixinBind.vue'

interface IEmits {
  (e: 'success', data: SC.User.RootInfo): void
}

const emits = defineEmits<IEmits>()

const isLoadQrcodeError = ref<string>()

let wxQrcode: HTMLIFrameElement | null = null

const showQrcode = defineModel<boolean>({ required: true })

watch(showQrcode, (val) => {
  if (val && !wxQrcode) {
    getWeixinQrcode()
  }
}, { immediate: true })

async function setWeixinQrcode(src = '') {
  const iframe = await createIframe('weixin_login', src)

  wxQrcode = iframe
}

const codeLoading = ref(false)

async function getWeixinQrcode(init = false) {
  await nextTick()
  if (!wxQrcode || init) {
    codeLoading.value = true
    try {
      const url = await ServiceWechatGetQrcode()
      isLoadQrcodeError.value = ''
      setWeixinQrcode(url)
    }
    catch (error) {
      isLoadQrcodeError.value = (error as any).data?.errmsg || '二维码获取失败'
    }
    finally {
      codeLoading.value = false
    }
  }
}

const refresh = () => getWeixinQrcode(true)

function onOpenQrcode() {
  ServiceWechatGetQrcode().then((url) => {
    if (window.top) {
      window.top.location = url
    }
  })
}

const openWeixinBind = ref(false)
function onOpenWeixinBind() {
  openWeixinBind.value = true
}

defineExpose({
  onOpenWeixinBind,
})
</script>

<template>
  <div class="login-weixin h-1 min-h-500px w-full flex flex-col justify-center dark:text-white">
    <div class="py-2 text-center font-bold">
      <div class="mb-1 text-xl">
        {{ openWeixinBind ? '绑定微信' : '扫码登录' }}
      </div>
      <div class="text-base">
        微信扫描，安全登录
      </div>
    </div>
    <div v-show="!openWeixinBind" v-loading="codeLoading" class="flex-col flex-1">
      <div id="weixin_login" class="flex-1">
        <div
          v-if="isLoadQrcodeError" class="h-full flex-c flex-col border-2 border-gray/10 border-solid text-lg"
          @click="refresh"
        >
          <span>{{ isLoadQrcodeError }}</span>
          <ElButton link>
            <span class="mt-1 text-base underline transition-all duration-300 dark:text-white hover:text-blue">重新加载</span>
          </ElButton>
        </div>
      </div>

      <div class="ml-2 mt-2 flex-ac">
        <RefreshCheckbox />
      </div>

      <div class="flex-jb p-5">
        <ElButton link @click="refresh">
          <span class="flex-ac underline transition-all duration-300 dark:text-white hover:text-blue">
            <i class="i-ant-design:reload-outlined" />
            刷新二维码
          </span>
        </ElButton>
        <ElButton link @click="onOpenQrcode">
          <span class="underline transition-all duration-300 dark:text-white hover:text-blue">
            扫码登录失败，试试这个？
          </span>
        </ElButton>
      </div>
    </div>
    <template v-if="openWeixinBind">
      <WeixinBind v-model="openWeixinBind" @success="emits('success', $event)" />
    </template>
  </div>
</template>
