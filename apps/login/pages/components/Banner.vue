<script setup lang="ts">
interface IProps {
  height?: string
  carousel: string[]
}
interface IEmits {
  (e: 'update:modelValue', val: any): void
}

const props = withDefaults(defineProps<IProps>(), {
  height: '400px',
})

const emits = defineEmits<IEmits>()
</script>

<template>
  <div class="banner-box flex-c">
    <el-carousel
      :height="props.height"
      class="h-full w-full"
    >
      <el-carousel-item
        v-for="(item, index) in carousel"
        :key="index"
      >
        <img
          class="h-full w-full object-contain"
          :src="item"
          alt=""
        >
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
