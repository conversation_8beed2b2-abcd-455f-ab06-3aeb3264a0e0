export function setUrlHref() {
  const { origin, hash } = window.location
  window.history.pushState({}, '', `${origin}/${hash}`)
}

export function createIframe(id: string, src: string) {
  return new Promise<HTMLIFrameElement>((resolve, reject) => {
    const weixinDiv = document.getElementById(id)

    if (!weixinDiv) {
      reject(new Error('weixinDiv is null'))
      return
    }

    weixinDiv.innerHTML = ''

    const iframe = document.createElement('iframe')

    iframe.id = 'weixin_login_iframe'
    iframe.src = src
    iframe.width = '100%'
    iframe.height = '100%'
    iframe.setAttribute('frameborder', '0')
    iframe.onload = () => {
      resolve(iframe)
    }
    weixinDiv.appendChild(iframe)
  })
}
