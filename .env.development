# 应用配置面板
VITE_APP_SETTING = true
# 页面标题
VITE_APP_TITLE = 深安先锋
# 代理接口前缀
VITE_APP_API_PROXY_PREFIX = /api
# 接口请求地址，会设置到 axios 的 baseURL 参数上
VITE_APP_API_BASEURL = https://tst-saxf.shencom.cn
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VITE_APP_DEBUG_TOOL =
# 是否禁用开发者工具，可防止被调试
VITE_APP_DISABLE_DEVTOOL = false

# 是否开启代理
VITE_OPEN_PROXY = false
# 是否开启开发者工具
VITE_OPEN_DEVTOOLS = true

# 环境
VITE_APP_ENV = development
# 应用 SCID
VITE_APP_SCID = sc047f71a5d4ef1134
