import type { Preset } from 'unocss'

export interface EnterAnimationOptions {
  maxChild?: number
  baseDelay?: number
  duration?: number
  timing?: string
}

export function presetEnterAnimation(options: EnterAnimationOptions = {}): Preset {
  const {
    maxChild = 5,
    baseDelay = 0.1,
    duration = 0.3,
    timing = 'ease-in-out',
  } = options

  return {
    name: 'unocss-preset-enter-animation',
    rules: [
      [/^enter-(x|y)$/, ([_, direction], { rawSelector }) => {
        if (rawSelector.startsWith('-enter-')) {
          return `.${rawSelector}{opacity: 0;${direction === 'x' ? 'transform: translateX(-50px)' : 'transform: translateY(-50px)'} }`
        }
        return {
          opacity: '0',
          transform:
            direction === 'x' ? 'translateX(50px)' : 'translateY(50px)',
        }
      }],
    ],
    preflights: [
      {
        getCSS: () => {
          const animations: string[] = []

          // 生成nth-child动画样式
          for (let i = 1; i <= maxChild; i++) {
            const delay = baseDelay * i
            animations.push(`
              .enter-x:nth-child(${i}) { animation: enter-x-animation ${duration}s ${timing} ${delay}s forwards; }
              .enter-y:nth-child(${i}) { animation: enter-y-animation ${duration}s ${timing} ${delay}s forwards; }
              .-enter-x:nth-child(${i}) { animation: enter-x-animation ${duration}s ${timing} ${delay}s forwards; }
              .-enter-y:nth-child(${i}) { animation: enter-y-animation ${duration}s ${timing} ${delay}s forwards; }
            `)
          }

          // 返回完整的CSS，包括关键帧定义
          return `
            @keyframes enter-x-animation {
              to {
                opacity: 1;
                transform: translateX(0);
              }
            }
            @keyframes enter-y-animation {
              to {
                opacity: 1;
                transform: translateY(0);
              }
            }
            ${animations.join('\n')}
          `
        },
      },
    ],
    theme: {
      animation: {
        durations: {
          float: '5s',
        },
        counts: {
          float: 'infinite',
        },
        timingFns: {
          float: 'linear',
        },
        keyframes: {
          'accordion-down': {
            from: { height: '0' },
            to: { height: 'var(--radix-accordion-content-height)' },
          },
          'accordion-up': {
            from: { height: 'var(--radix-accordion-content-height)' },
            to: { height: '0' },
          },
          'collapsible-down': {
            from: { height: '0' },
            to: { height: 'var(--radix-collapsible-content-height)' },
          },
          'collapsible-up': {
            from: { height: 'var(--radix-collapsible-content-height)' },
            to: { height: '0' },
          },
          'float': '{10% { transform: translateY(0) } 50% { transform: translateY(-20px) } 100% { transform: translateY(0) }}',
        },
      },
    },
  }
}
