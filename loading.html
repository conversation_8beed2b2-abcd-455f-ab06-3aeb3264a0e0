<style>
  /* From Uiverse.io by g<PERSON><PERSON><PERSON><PERSON><PERSON> */
  .loader {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
    animation: loader_513 2s linear infinite;
  }

  .shape {
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 80px;
    border-radius: 15px;
  }

  .shape {
    background-color: hsl(var(--primary, 212 100% 45%));
    animation: rectangle_513 4s linear infinite;
    animation-delay: 2s;
  }

  @keyframes loader_513 {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes rectangle_513 {
    0% {
      transform: scale(1, 1);
      border-radius: 15px;
    }

    25% {
      border-radius: 30px;
      box-shadow: 0px 0px 5px rgba(133, 133, 133, 0.523);
      background-color: hsl(var(--primary, 212 100% 45%) / 50%);
      transform: scale(0.9);
    }

    50% {
      border-radius: 20px;
      transform: scale(1.4);
      box-shadow: 2px 5px 50px rgba(90, 90, 90, 0.206);
    }
  }

  .loading-container {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    user-select: none;
    background-color: snow;
    background-color: hsl(var(--background));
  }

  .title {
    margin-top: 66px;
    font-size: 28px;
    font-weight: 600;
    color: rgb(0 0 0 / 85%);
  }

  .dark .title {
    color: #fff;
  }
</style>
<div class="loading-container">
  <div class="loader">
    <div class="shape"></div>
  </div>

  <div class="title">%VITE_APP_TITLE%</div>
</div>
