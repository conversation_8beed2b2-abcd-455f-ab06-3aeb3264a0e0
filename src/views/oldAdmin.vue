<script setup lang="ts">
import type { TdsMsg } from '@taptap/tds-msg/es/common'
import { IframePlatformService, ServiceToken } from '@/service'
import { ServiceUser } from '@/service/user'
import useAdminStore from '@/store/modules/admin'
import useRouteStore from '@/store/modules/route'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import { UserInfo } from '@admin/utils'
import TdsServer from '@taptap/tds-msg/es/server'

const router = useRouter()
const route = useRoute()
const settingsStore = useSettingsStore()
const userStore = useUserStore()
const routeStore = useRouteStore()
const { setCustomTitle } = useMainPage()
const { generateI18nTitle } = useMenu()

const url = ref(`${IframePlatformService.handleUrl()}`)
const iframeRef = useTemplateRef<HTMLIFrameElement>('iframeRef')

let server: TdsServer

watch(() => route.fullPath, (newVal) => {
  console.log('[父页面] 路由监控', !!server, newVal, route.meta?.admin)
  if (server && route.meta?.admin) {
    sendGo(newVal)
  }
})

watch(() => settingsStore.currentColorScheme, (newVal) => {
  server.msg({ type: 'theme', value: newVal })
})

function sendGo(p: string, options = {}) {
  console.log('[父页面] sendGo', p, options)

  server.go(p, '', {
    query: route.query,
    type: 'push',
    ...options,
  })
}

async function sendToken() {
  const token = UserInfo.getToken()
  if (!token) {
    await ServiceToken.login()
  }
  server.msg({ type: 'token', value: { token: UserInfo.getToken(), expiration: UserInfo.getTokenExpiration() } })
}

function sendUserInfo() {
  const root: SC.User.RootInfo = {
    additionalInformation: UserInfo.getUserInfo()!,
    value: UserInfo.getToken()!,
    expiration: UserInfo.getTokenExpiration()!,
    refreshToken: {
      value: UserInfo.getRefreshToken()!,
      expiration: UserInfo.getRefreshTokenExpiration()!,
    },
  }
  server.msg({ type: 'userInfo', value: root })
}

function sendPermission() {
  const permissions = toRaw(userStore.permissions)
  const menus = ServiceUser.getCacheMenus()
  server.msg({ type: 'permission', value: { menus, permissions } })
}

function updateTitle(title?: string) {
  if (title) {
    setCustomTitle(generateI18nTitle(title))
  }
  IframePlatformService.setRouteTitleList(settingsStore.customTitleList)
}

onMounted(async () => {
  console.log('[父页面] onMounted', route)

  const iframe = iframeRef.value
  await nextTick()

  if (!iframe) {
    return
  }
  const isOldPlatform = route.path.includes(`/${IframePlatformService.routerName}`)

  server = new TdsServer(IframePlatformService.host, () => iframe, {
    onRefreshTicket: (data) => {
      console.log('[父页面] onRefreshTicket', data)
      if (data.type === 'tdsMsg.refreshTicket') {
        sendToken()
      }
    },
    onError: (code) => {
      console.error(`[父页面] error: ${code}`)
    },
    onSyncPath: async (data: TdsMsg<{ path: string, options: Record<string, any> }>) => {
      console.log('[父页面] syncPath', data.payload?.path, data)

      if (data.payload?.path) {
        const { options, path } = data.payload
        const menuItem = routeStore.flatRoutesAll.find((item) => {
          return path === item.path?.replace(/\/iframe-middleware\/\d+/, '')
        })

        if (menuItem) {
          console.log('[父页面] 跳转页面:', menuItem.path)
          router.push({ path: menuItem.path, query: options.query, force: true })
        }
        else if (path.includes('/fallback')) {
          router.push({ path, query: options.query, force: true })
        }
        else {
          const t = `/${IframePlatformService.routerName}${path}`
          console.log('[父页面] 跳转页面:', t)
          router.push({ path: t, query: options.query, force: true }).then(() => {
            updateTitle(options.title)
          })
        }
      }
    },
    onMessage: (data) => {
      const { type, value } = data.payload
      if (type === 'routeTitle') {
        console.log('[父页面] 收到 routeTitle 事件', data)
        updateTitle(value.title)
      }
      else if (type === 'permission') {
        sendPermission()
      }
      else {
        console.log('[父页面] 收到未知事件', data)
      }
    },
  })
  server.once('onReady', (data) => {
    useAdminStore().isLoad = true
    console.log('[父页面] onReady', data, route)

    if (route.meta.admin && isOldPlatform) {
      sendGo(route.fullPath, { type: 'replace' })
    }
    // sendToken()
    sendUserInfo()
    sendPermission()
    server.msg({ type: 'theme', value: settingsStore.currentColorScheme })
    server.msg({ type: 'style', value: IframePlatformService.style })
  })
})
</script>

<template>
  <div class="old-admin absolute left-0 top-0 h-full w-full flex flex-col dark:bg-black">
    <Transition
      mode="out-in" appear
      :name="settingsStore.settings.mainPage.enableTransition ? settingsStore.settings.mainPage.transitionMode : ''"
    >
      <iframe ref="iframeRef" :data-path="url" :src="url" frameborder="0" class="h-full w-full flex-1" />
    </Transition>
  </div>
</template>
