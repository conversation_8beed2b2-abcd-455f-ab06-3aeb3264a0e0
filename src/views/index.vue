<script setup lang="ts">
import { IframePlatformService, ServiceSite } from '@/service'
import useAdminStore from '@/store/modules/admin'
import useRouteStore from '@/store/modules/route'
import { onMounted } from 'vue'

const loading = ref(true)
const router = useRouter()
const adminStore = useAdminStore()
const routeStore = useRouteStore()

const { getId, closeById } = useTabbar()
watch(() => adminStore.isLoad, (value) => {
  // if (value) {
  //   replaceHome()
  // }

  // loading.value = false
})

async function replaceHome() {
  const homeId = getId()
  const path = ServiceSite.home()
  if (path !== '/') {
    const menuItem = routeStore.flatRoutesAll.find((item) => {
      return path === item.path?.replace(/\/iframe-middleware\/\d+/, '')
    })
    if (menuItem) {
      await router.push({ path: menuItem.path, force: true })
      closeById(homeId)
    }
    else {
      await router.replace(`/${IframePlatformService.routerName}${path}`)
      closeById(homeId)
    }
  }
}

onMounted(() => {
  // if (adminStore.isLoad) {
  //   replaceHome()
  // }
})
</script>

<template>
  首页
  <div v-loading="loading" class="h-screen" />
</template>
