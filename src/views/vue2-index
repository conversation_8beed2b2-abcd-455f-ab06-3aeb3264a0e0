<template>
  <div class="workbenchPage">
    <!-- 数据概览 -->
    <el-row :gutter="20"
            class="overview-row">
      <el-col :span="12">
        <el-row class="overview-card-row padding-16">
          <div class="header-title">
            <i></i> 数据概览
          </div>
          <el-col :span="6"
                  v-for="item in overviewList"
                  :key="item.label">
            <div class="overview-card">
              <div class="overview-num sc-font-primary">{{ item.value }}</div>
              <div class="overview-label">{{ item.label }}</div>
            </div>
          </el-col>
        </el-row>
        <!-- 快捷功能 -->
        <el-row class="quick-row padding-16">
          <div class="header-title">
            <i></i> 快捷功能
          </div>
          <el-col :span="24">
            <div class="quick-func">
              <div v-for="item in quickList"
                   :key="item.label"
                   class="quick-item"
                   @click="goRoute(item.id)">
                <i :class="item.icon"
                   class="quick-icon"></i>
                <div class="quick-label">{{ item.label }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="6">
        <div class="todo-card padding-16">
          <div class="flex flex-jsb flex-ac">
            <div class="header-title">
              <i></i> 代办事项
            </div>
            <div class="todo-header">
              <el-checkbox-group v-model="todoTab"
                                 size="mini"
                                 class="todo-tabs">
                <el-checkbox-button label="今日">今日</el-checkbox-button>
                <el-checkbox-button label="昨日">昨日</el-checkbox-button>
                <el-checkbox-button label="历史全部">历史全部</el-checkbox-button>
              </el-checkbox-group>
            </div>
          </div>
          <div>
            <div class="todo-header">
              <div class="mt-10">
                <el-radio v-for="item in radioList1"
                          :key="item.value"
                          size="mini"
                          v-model="radio1"
                          :label="item.value"
                          border>{{ item.label }}</el-radio>

              </div>
            </div>
          </div>
          <div class="flex flex-jse mt-10 moreBtn">
            <span class="todo-more">更多<i class="el-icon-arrow-right"></i>
            </span>
          </div>
          <div class="todo-list"
               v-if="todoList.length">
            <div v-for="(item, idx) in todoList"
                 :key="idx"
                 class="todo-item"
                 @click="goRoute(item.id)">
              <span class="todo-date">{{ item.date }}</span>
              <span class="todo-title">{{ item.title }}</span>
              <span class="todo-status">{{ item.status }}</span>
            </div>
          </div>
          <div v-else
               class="todo-empty">
            组织内成员暂无待办事项，后续有需求再优化，先保留待办事项模块。
          </div>
        </div>
      </el-col>
      <!-- 待阅事项 -->
      <el-col :span="6">
        <div class="todo-card padding-16">
          <div class="flex flex-jsb flex-ac">
            <div class="header-title">
              <i></i> 待阅事项
            </div>
            <div class="todo-header">
              <el-checkbox-group v-model="readTab"
                                 size="mini"
                                 class="todo-tabs">
                <el-checkbox-button label="今日">今日</el-checkbox-button>
                <el-checkbox-button label="昨日">昨日</el-checkbox-button>
                <el-checkbox-button label="历史全部">历史全部</el-checkbox-button>
              </el-checkbox-group>
            </div>
          </div>
          <div>
            <div class="todo-header">
              <div class="mt-10">
                <el-radio v-for="item in radioList2"
                          :key="item.value"
                          size="mini"
                          v-model="radio2"
                          :label="item.value"
                          border>{{ item.label }}</el-radio>

              </div>
            </div>
          </div>
          <div class="flex flex-jse mt-10 moreBtn">
            <span class="todo-more">更多<i class="el-icon-arrow-right"></i>
            </span>
          </div>

          <div class="todo-list">
            <div v-for="(item, idx) in readList"
                 :key="idx"
                 class="todo-item">
              <span class="todo-date">{{ item.date }}</span>
              <span class="todo-title">{{ item.title }}</span>
              <span class="todo-status">{{ item.status }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 下方图表区 -->
    <el-row :gutter="20"
            class="chart-row">
      <el-col :span="12">
        <div class="chart-card">
          <div class="flex flex-jsb flex-ac">
            <div class="header-title">
              <i></i> 违规告警趋势图
            </div>
            <el-date-picker v-model="trendDate"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            size="mini"
                            style="margin-left: 16px;" />
          </div>

          <div class="chart-content">
            <report-chart ref="trend"
                          class="flex-1"
                          title=""
                          v-bind="trend" />
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="chart-card">
          <div class="flex flex-jsb flex-ac">
            <div class="header-title">
              <i></i> 工程区域分布
            </div>
            <el-select v-model="regionType"
                       size="mini"
                       style="margin-left: 16px;">
              <el-option label="新桥街道"
                         value="新桥街道" />
            </el-select>
          </div>

          <div class="chart-content">
            <report-chart ref="trend2"
                          class="flex-1"
                          title=""
                          v-bind="trend2" />
          </div>
        </div>
      </el-col>
    </el-row>

  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { ChartConfig } from '@/modules/shenanPioneer/components/report';
import ReportChart from '@/modules/shenanPioneer/components/report/chart/index.vue';

@Component({
  components: {
    ReportChart,
  },
})
export default class WorkbenchPage extends Vue {
  todoTab = ['今日'];

  readTab = ['我的待阅'];

  trendDate = [];

  regionType = '新桥街道';

  overviewList = [
    { label: '备案施工单位', value: 30 },
    { label: '工程总数', value: 52 },
    { label: '施工中工程', value: 52 },
    { label: '总工程额（万）', value: 0 },
  ];

  quickList = [
    { id: 1, label: '我的工程', icon: 'el-icon-folder', route: '/my-project' },
    { id: 2, label: '告警列表', icon: 'el-icon-warning', route: '/alarm-list' },
    { id: 3, label: '工程监控', icon: 'el-icon-video-camera', route: '/project-monitor' },
    { id: 4, label: '小散场景', icon: 'el-icon-s-grid', route: '/sporadic-scene' },
    { id: 5, label: '统计分析', icon: 'el-icon-data-analysis', route: '/stat-analysis' },
  ];

  todoList = [
    { id: 1, date: '2025.05.01', title: '工程【小散工程名称1】', status: '已接入监管' },
    { id: 2, date: '2025.05.01', title: '工程【小散工程名称1】', status: '已接入监管' },
    { id: 3, date: '2025.05.01', title: '工程【小散工程名称1】', status: '已接入监管' },
    { id: 4, date: '2025.05.01', title: '工程【小散工程名称1】', status: '已接入监管' },
    { id: 5, date: '2025.05.01', title: '未正常佩戴安全帽，所属工程【小散工程名称1】', status: '' },
  ];

  radio2 = 1;

  radioList2 = [
    {
      label: '我的待阅',
      value: 1,
    },
    {
      label: '我的已阅',
      value: 2,
    },
  ];

  radio1 = 1;

  radioList1 = [
    {
      label: '我的待办',
      value: 1,
    },
    {
      label: '我的已办',
      value: 2,
    },
  ];

  readList = [
    { date: '2025.05.01', title: '工程【小散工程名称1】', status: '已接入监管' },
    { date: '2025.05.01', title: '工程【小散工程名称1】', status: '已接入监管' },
    { date: '2025.05.01', title: '工程【小散工程名称1】', status: '已接入监管' },
    { date: '2025.05.01', title: '工程【小散工程名称1】', status: '已接入监管' },
    { date: '2025.05.01', title: '未正常佩戴安全帽，所属工程【小散工程名称1】', status: '' },
  ];

  trend: ChartConfig = {
    columns: [
      { label: '日期', prop: 'date' },
      { label: '告警次数', prop: 'cnt' },
    ],
    table: { showSummary: false },
    chart: {
      api: '',
      type: 'line',
      options: {
        grid: { left: '40px', right: '40px' },
      },
    },
  };

  trend2: ChartConfig = {
    columns: [
      { label: '日期', prop: 'date' },
      { label: '工程数量', prop: 'cnt' },
    ],
    table: { showSummary: false },
    chart: {
      api: '',
      type: 'bar',
      options: {
        grid: { left: '40px', right: '40px' },
      },
    },
  };

  goRoute(id: number) {
    // 路由跳转
    this.$router.push(`/project-detail/${id}`);
  }
}
</script>

<style lang="scss" scoped>
.workbenchPage {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 24px;

  .overview-row {
    margin-bottom: 20px;
  }
  .overview-card-row {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px #f0f1f2;
  }
  .overview-card {
    background: #fff;
    border-radius: 8px;
    text-align: center;
    padding: 16px 0;
    .overview-num {
      font-size: 32px;
      font-weight: bold;
      margin-bottom: 4px;
    }
    .overview-label {
      color: #888;
      font-size: 14px;
    }
  }
  .sc-font-primary {
    color: #409eff;
  }
  .padding-16 {
    padding: 16px;
  }
  .todo-card {
    background: #fff;
    border-radius: 8px;
    min-height: 180px;
    box-shadow: 0 2px 8px #f0f1f2;
    .todo-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .todo-tabs {
        ::v-deep {
          .el-checkbox-button__inner {
            padding: 3px 10px;
          }
        }
      }
    }
    .todo-more {
      color: #409eff;
      font-size: 13px;
    }
    .moreBtn {
      justify-content: flex-end;
      margin-bottom: 10px;
      cursor: pointer;
    }
    .todo-list {
      .todo-item {
        font-size: 13px;
        margin-bottom: 6px;
        cursor: pointer;
        .todo-date {
          color: #888;
          margin-right: 8px;
        }
        .todo-title {
          flex: 1;
        }
        .todo-status {
          color: #67c23a;
          margin-left: 8px;
        }
      }
    }
    .todo-empty {
      color: #f56c6c;
      font-size: 14px;
      padding: 16px 0;
      text-align: center;
    }
  }
  .quick-row {
    margin-bottom: 20px;
    margin-top: 20px;
    background: #fff;
  }
  .quick-func {
    display: flex;
    border-radius: 8px;
    margin-top: 16px;
    justify-content: space-around;
    .quick-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: color 0.2s;
      &:hover {
        color: #409eff;
      }
      .quick-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }
      .quick-label {
        font-size: 14px;
      }
    }
  }
  .chart-row {
    margin-bottom: 20px;
  }
  .chart-card {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    min-height: 280px;
    box-shadow: 0 2px 8px #f0f1f2;
    .chart-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-weight: bold;
      font-size: 15px;
    }
    .chart-content {
      min-height: 180px;
    }
  }
  .header-title {
    font-size: 16px;
    display: flex;
    align-items: center;
    i {
      width: 4px;
      height: 16px;
      background: #409eff;
      margin-right: 5px;
    }
  }
}
</style>
