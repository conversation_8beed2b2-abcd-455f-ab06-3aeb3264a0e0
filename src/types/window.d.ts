import type * as api from '@/api/api'
import type * as service from '@/service'
import type * as utils from '@/utils'
import type { Instance } from '@shencom/request'
import type { RouteRecordRaw } from 'vue-router'

declare global {
  interface Window {
    $global: {
      $api: typeof api
      $http: Instance
      $service: typeof service
      $utils: typeof utils
    }
    apps: {
      [key: string]: {
        // app: any
        routes: RouteRecordRaw | RouteRecordRaw[] | Route.recordMainRaw
        version: string
        // create: (router: Router) => void;
        // mount?: (container: HTMLElement) => void;
        // addRoute?: (router: Router) => void;
        // unmount?: () => void;
      }
    }
  }
}
