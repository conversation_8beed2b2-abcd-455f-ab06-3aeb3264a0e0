/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Auth: typeof import('./../components/Auth/index.vue')['default']
    Chip: typeof import('./../components/Chip/index.vue')['default']
    ColorfulCard: typeof import('./../components/ColorfulCard/index.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    Fallback: typeof import('./../../packages/components/src/fallback/index.ts')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    FixedActionBar: typeof import('./../components/FixedActionBar/index.vue')['default']
    HBadge: typeof import('./../layouts/ui-kit/HBadge.vue')['default']
    HButton: typeof import('./../layouts/ui-kit/HButton.vue')['default']
    HCheckList: typeof import('./../layouts/ui-kit/HCheckList.vue')['default']
    HDialog: typeof import('./../layouts/ui-kit/HDialog.vue')['default']
    HDropdown: typeof import('./../layouts/ui-kit/HDropdown.vue')['default']
    HDropdownMenu: typeof import('./../layouts/ui-kit/HDropdownMenu.vue')['default']
    HInput: typeof import('./../layouts/ui-kit/HInput.vue')['default']
    HKbd: typeof import('./../layouts/ui-kit/HKbd.vue')['default']
    HSelect: typeof import('./../layouts/ui-kit/HSelect.vue')['default']
    HSlideover: typeof import('./../layouts/ui-kit/HSlideover.vue')['default']
    HTabList: typeof import('./../layouts/ui-kit/HTabList.vue')['default']
    HToggle: typeof import('./../layouts/ui-kit/HToggle.vue')['default']
    HTooltip: typeof import('./../layouts/ui-kit/HTooltip.vue')['default']
    IconPicker: typeof import('./../components/IconPicker/index.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImagesUpload: typeof import('./../components/ImagesUpload/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    LayoutContainer: typeof import('./../components/LayoutContainer/index.vue')['default']
    LoginForm: typeof import('./../components/LoginForm/index.vue')['default']
    NotAllowed: typeof import('./../components/NotAllowed/index.vue')['default']
    PageHeader: typeof import('./../components/PageHeader/index.vue')['default']
    PageMain: typeof import('./../components/PageMain/index.vue')['default']
    PcasCascader: typeof import('./../components/PcasCascader/index.vue')['default']
    RegisterForm: typeof import('./../components/RegisterForm/index.vue')['default']
    ResetPasswordForm: typeof import('./../components/ResetPasswordForm/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScSms: typeof import('./../../packages/components/src/ScSms/index.ts')['default']
    SearchBar: typeof import('./../components/SearchBar/index.vue')['default']
    Sparkline: typeof import('./../components/Sparkline/index.vue')['default']
    SpinkitLoading: typeof import('./../components/SpinkitLoading/index.vue')['default']
    StorageBox: typeof import('./../components/StorageBox/index.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    SystemInfo: typeof import('./../components/SystemInfo/index.vue')['default']
    Trend: typeof import('./../components/Trend/index.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
