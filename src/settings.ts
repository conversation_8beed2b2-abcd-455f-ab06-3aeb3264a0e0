import type { RecursiveRequired, Settings } from '#/global'
import settingsDefault from '@/settings.default'
import { defaultsDeep } from 'lodash-es'

const globalSettings: Settings.all = {
  app: {
    colorScheme: '',
    lightTheme: 'classic',
    // darkTheme: 'night',
    enablePermission: true,
    enableDynamicTitle: true,
    enableErrorLog: true,
    routeBaseOn: 'backend',
    enableWatermark: true,
  },
  menu: {
    mode: 'head',
    style: 'line',
    isRounded: true,
    enableSubMenuCollapseButton: true,
    enableHotkeys: true,
  },
  layout: {
    enableMobileAdaptation: true,
  },
  mainPage: {
    transitionMode: 'slide-right',
    iframeCacheMax: 9,
  },
  topbar: {
    mode: 'fixed',
  },
  tabbar: {
    style: 'fashion',
    enableIcon: true,
    dblclickAction: 'reload',
    enableMemory: true,
    enable: true,
    enableHotkeys: true,
  },
  toolbar: {
    breadcrumb: true,
    fullscreen: true,
    pageReload: true,
    colorScheme: true,
    favorites: true,
  },
  breadcrumb: {
    style: 'modern',
    enableMainMenu: true,
  },
  home: {
    fullPath: '/',
  },
  userPreferences: {
    enable: true,
  },
  copyright: {
    enable: true,
    dates: `2020-${new Date().getFullYear()}`,
    company: '',
    website: '',
    beian: '粤ICP备05099152号',
  },
}
const setting = defaultsDeep(globalSettings, settingsDefault) as RecursiveRequired<Settings.all>

export default setting
