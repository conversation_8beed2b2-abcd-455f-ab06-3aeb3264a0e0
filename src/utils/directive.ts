import type { App, DirectiveBinding } from 'vue'
import mediumZoom from 'medium-zoom'

export default function directive(app: App) {
  app.directive('auth', (el: HTMLElement, binding: DirectiveBinding) => {
    if (binding.modifiers.all ? useAuth().authAll(binding.value) : useAuth().auth(binding.value)) {
      el.style.display = ''
    }
    else {
      el.style.display = 'none'
    }
  })
  app.directive('timeago', {
    mounted: (el, binding) => {
      el.textContent = useTimeago().format(binding.value)
      if (binding.modifiers.interval) {
        el.__timeagoSetInterval__ = setInterval(() => {
          el.textContent = useTimeago().format(binding.value)
        }, 1000)
      }
    },
    beforeUnmount: (el, binding) => {
      if (binding.modifiers.interval) {
        clearInterval(el.__timeagoSetInterval__)
      }
    },
  })
  // 注册 medium-zoom 指令
  app.directive('zoomable', {
    mounted: (el) => {
      mediumZoom(el, {
        background: 'var(--g-bg)',
      })
    },
  })
}
