import { axios } from '@/api'
import { getCacheOrganizationId } from '@/store/modules/organization'
import { Storages, ValidateURL } from '@/utils'
import { IframePlatformService } from './iframe-platform'

export interface MenuItem {
  id: string
  url?: string
  children?: MenuItem[]
  name: string
  displayName: string
  payload?: string
  icon?: string
}

interface Route {
  path: string
  name: string
  redirect?: string
  component: string
  meta: {
    id: string
    icon: string
    title: string
    cache: boolean
    admin: boolean
  }
  children?: Route[]
}

function urlToPath(url: string, id: string) {
  const isUrl = ValidateURL(url)
  if (isUrl) {
    return `/${IframePlatformService.routerName}/external/${encodeURIComponent(url)}`
  }
  return `/${IframePlatformService.routerName}/${id}${url}`
}

function childrenToFirstPath(children: MenuItem[]) {
  if (!children.length) {
    return ''
  }

  const first = children[0]

  if (first?.url) {
    return urlToPath(first.url, first.id)
  }

  return childrenToFirstPath(first.children || [])
}

function parseChildren(i: MenuItem) {
  const { id, children = [], url, name, displayName, icon = 'i-ri:external-link-fill' } = i

  const res: Route = {
    component: 'Layout',
    path: '',
    name,
    meta: {
      id,
      icon,
      title: displayName,
      cache: true,
      admin: true,
    },
  }

  if (!url && !children.length) {
    res.path = `/empty/${id}`
    return res
  }

  if (url) {
    res.path = urlToPath(url, id)
  }
  else {
    const firstPath = childrenToFirstPath(children)
    res.path = `/empty/${id}`
    if (firstPath) {
      res.redirect = firstPath
    }
  }

  if (children.length) {
    res.children = children.map((j: MenuItem) => parseChildren(j))
  }

  return res
}

function formatMenuToRoute(data: MenuItem[]) {
  const menus = data.map((item: MenuItem) => ({
    meta: {
      title: item.displayName,
      icon: item.icon || 'ion:settings-sharp',
      admin: true,
    },
    children: item.children?.map((i: MenuItem) => parseChildren(i)) || [],
  }))

  return menus
}

interface Organization {
  id: string
  name: string
}

/**
 * 获取组织列表
 */
async function ApiGetOrganization() {
  const api = `/sporadic-project/fn/rmsv3/members/type/relate/show/all/organization`
  const { data } = await axios.post<Organization[]>(api, {})
  return data
}

/**
 * 不传 organizationId 时，获取上一次切换到的那个组织，如果没有就返回第一个组织
 * 传 organizationId 时，进行组织切换，保存提交的组织id
 */
export async function ApiSwitchOrganization(organizationId?: string) {
  const { data } = await axios.post<Organization>(`/sporadic-project/sys/role/user/organization/switch`, {
    organizationId,
  })
  return data
}

/**
 * 获取用户菜单树
 */
async function ApiGetMenus() {
  const body = { sorts: [{ orderField: 'sort', orderType: 'asc' }] }
  const organizationId = getCacheOrganizationId()

  const { data } = await axios.post<MenuItem[]>(`/sporadic-project/sys/menu/tree/my`, body, {
    headers: {
      organizationId,
    },
  })
  return data
}

/** 获取用户权限列表 */
async function ApiGetPermission() {
  const organizationId = getCacheOrganizationId()
  const { data } = await axios.get<string[]>(`/sporadic-project/sys/permission/allPermission`, {}, {
    headers: {
      organizationId,
    },
  })
  return data
}

class CacheUserConfig {
  static getCacheOrganization() {
    return Storages.getUser<Organization[]>('organization') || []
  }

  static getCachePermissions() {
    return Storages.getUser<string[]>('permissions') || []
  }

  static getCacheMenus() {
    return Storages.getUser<MenuItem[]>('menus') || []
  }

  protected static setCacheOrganization(organization: Organization[]) {
    Storages.setUser('organization', organization, 60)
  }

  protected static setCachePermissions(permissions: string[]) {
    Storages.setUser('permissions', permissions, 60)
  }

  protected static setCacheMenus(menus: MenuItem[]) {
    Storages.setUser('menus', menus, 60)
  }

  static hasCachePermissions() {
    return this.getCachePermissions().length > 0
  }

  static hasCacheMenus() {
    return this.getCacheMenus().length > 0
  }

  static removeCachePermissions() {
    Storages.removeUser('permissions')
  }

  static removeCacheMenus() {
    Storages.removeUser('menus')
  }

  static removeCacheOrganization() {
    Storages.removeUser('organization')
  }
}

export class ServiceUser extends CacheUserConfig {
  static async requestOrganization() {
    let organization = this.getCacheOrganization()
    if (!organization?.length) {
      organization = await ApiGetOrganization()
      this.setCacheOrganization(organization)
    }
    return organization
  }

  static async requestMenus() {
    let menus = this.getCacheMenus()
    if (!menus?.length) {
      menus = await ApiGetMenus()
      this.setCacheMenus(menus)
    }
    return { data: formatMenuToRoute(menus) }
  }

  static async requestPermissions() {
    let permissions = this.getCachePermissions()
    if (!permissions?.length) {
      permissions = await ApiGetPermission()
      this.setCachePermissions(permissions)
      return permissions
    }
    return permissions
  }
}
