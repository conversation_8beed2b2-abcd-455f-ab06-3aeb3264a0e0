import { axios } from '@/api'
import { Storages } from '@admin/utils'
import { useUrlSearchParams } from '@vueuse/core'
import { ServiceResources } from './resources'
import imgLogoDark from '/assets/logo/logo-dark.svg'
import imgLogo from '/assets/logo/logo.svg'

export interface SiteConfig {
  /** 常见问题 */
  common_problem: string

  /** 网站帮助 */
  website_help: string

  /** 联系我们 */
  contact_us: string

  /** 法律声明 */
  law_notice: string

  /** 版权声明 */
  copyright_notice: string

  /** 人机校验方式：sms短信验证码，image图形验证码 */
  captcha_type: 'sms' | 'image'

  /** 是否开启验证：true开启，false关闭 */
  status: 'true' | 'false'

  /** ip调用规定时间 */
  ip_time: string

  /** ip调用限定次数 */
  ip_times: string

  /** ip触发图形验证码次数 */
  ip_captcha_times: string

  /** 用户调用规定时间 */
  user_time: string

  /** 用户调用限定次数 */
  user_times: string

  /** 用户触发图形验证码次数 */
  user_captcha_times: string

  /** 锁定期提示语 */
  lock_msg: string

  /** 用户服务协议及隐私保护政策 */
  user_agreement: string

  /** 默认密码变更时间 */
  pwd_default_change_time: string

  /** 定期变更密码频率 */
  pwd_frequency_of_changes: string

  /** 是否定期变更密码 */
  pwd_change_regularly: string

  /** 首次登录是否需调整密码 */
  pwd_first_login_change: string

  /** 密码其他信息配置 */
  pwd_other: string

  /** 密码长度 */
  pwd_length: string

  /** 密码验证正则表达式 */
  pwd_verify: string

  /** 是否开启扫码登录：0-关闭，1-开启 */
  scan_login_active: '0' | '1'

  /** 是否默认扫码登录 1-账密登录，2-二维码登录 */
  login_method: '1' | '2'

  /** 人机校验方式 image/msg */
  man_machine_calibration_method: 'image' | 'msg'

  /** 首页路由 */
  home_route: string

  /** 城市专区灰度测试角色列表 */
  city_gray_scale_test_role: string

  /** 城市专区灰度测试 */
  city_gray_scale_test: string

  /** 短信后缀 */
  msg_suffix: string

  /** 短信每月限量 */
  msg_month_limit: string

  /** 短信服务商 */
  msg_servicer: string

  /** 移动端站点背景 */
  wx_mng_config_background: string

  /** 移动端站点图标 */
  wx_mng_config_icon: string

  /** 移动端版权信息 */
  wx_mng_config_copyright: string

  /** 移动端域名信息 */
  wx_mng_config_info: string

  /** 移动端描述 */
  wx_mng_config_desc: string

  /** 移动端站点别名 */
  wx_mng_config_alias: string

  /** 移动端关键字 */
  wx_mng_config_keyword: string

  /** 移动端站点名称 */
  wx_mng_config_name: string

  /** 站点背景 */
  mng_config_background: string

  /** 站点图标 */
  mng_config_icon: string

  /** 版权信息 */
  mng_config_copyright: string

  /** 域名信息 */
  mng_config_info: string

  /** 描述 */
  mng_config_desc: string

  /** 站点别名 */
  mng_config_alias: string

  /** 关键字 */
  mng_config_keyword: string

  /** 站点名称 */
  mng_config_name: string

}

interface Site {
  id: string
  label: keyof SiteConfig
  value?: string
  description?: string
}

interface SiteExtension extends Partial<SiteConfig> { loginPage: string, title: string, logo: string }

const SITE_IMGS = ['mng_config_background', 'mng_config_icon']

/** 获取站点配置 */
async function ApiGetSite() {
  const { data } = await axios.post<Site[]>(`/service-uaa/backext/index`)
  return data
}

async function remoteImages(siteImgs: Site[]) {
  const ids = siteImgs.map(v => v.value!).filter(Boolean)
  const images = await ServiceResources.file.getData(ids)

  if (!images.length) {
    return {}
  }

  const config = siteImgs.reduce<Record<string, string>>((pre, cur) => {
    const img = images.find(e => e.id === cur.value)

    if (img) {
      pre[cur.label] = img.remoteUrl
    }

    return pre
  }, Object.create(null))

  return config
}

async function getSite() {
  const data = await ApiGetSite()

  const siteImgs: typeof data = []

  const site: SiteConfig = data.reduce((pre, cur) => {
    if (cur.value) {
      pre[cur.label] = cur.value
    }

    if (SITE_IMGS.includes(cur.label as string)) {
      siteImgs.push(cur)
    }

    return pre
  }, Object.create(null))

  const images = await remoteImages(siteImgs)

  return { ...site, ...images }
}

const defaultPasswordConfig = {
  pwd_verify: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).{8,}$',
  pwd_length: '8',
  pwd_other: '大小写字母+数字',
}

class CacheSite {
  private static _key = 'SiteConfig'

  static hasCache() {
    const data = this.getCache()
    return data && Object.keys(data).length > 0
  }

  static removeCache() {
    Storages.removeData(this._key)
  }

  protected static getCache() {
    return Storages.getData<SiteConfig>(this._key)
  }

  protected static setCache(data?: SiteConfig) {
    if (data) {
      Storages.setData(this._key, data, 60 * 8)
    }
    else {
      Storages.removeData(this._key)
    }
  }
}

export class ServiceSite extends CacheSite {
  static get title() {
    return this.config('mng_config_name') || import.meta.env.VITE_APP_TITLE
  }

  static get logo() {
    return this.config()?.logo
  }

  static get darkLogo() {
    return imgLogoDark
  }

  static get passwordConfig() {
    const pwd_length = this.config('pwd_length')
    const pwd_other = this.config('pwd_other')
    const pwd_verify = this.config('pwd_verify')!
    return {
      pattern: new RegExp(pwd_verify),
      message: `${pwd_length}位或${pwd_length}位以上，包含${pwd_other}组合`,
    }
  }

  static request = async () => {
    const site = await getSite()

    this.setCache(site)

    return site
  }

  static home(route?: string) {
    const site = ServiceSite.getCache()
    const params = useUrlSearchParams<{ redirect_url: string }>('history')
    let path = '/'
    let { redirect_url } = params
    if (redirect_url) {
      redirect_url = decodeURIComponent(redirect_url)
      if (redirect_url[0] !== '/') {
        redirect_url = `/${redirect_url}`
      }

      path = redirect_url
    }
    else if (route) {
      path = route
    }
    else if (site?.home_route) {
      if (site.home_route !== '/') {
        path = site.home_route
      }
    }
    return path
  }

  static config<T extends keyof SiteExtension>(key: T): SiteExtension[T] | undefined
  static config(): SiteExtension
  static config<T extends keyof SiteExtension>(key?: T) {
    const site = ServiceSite.getCache()

    const config: SiteExtension = {
      ...site,
      home_route: this.home(),
      /** 登录页面路由 */
      loginPage: '/login',
      title: site?.mng_config_name || '',
      logo: site?.mng_config_icon || imgLogo,
      pwd_verify: site?.pwd_verify ?? defaultPasswordConfig.pwd_verify,
      pwd_length: site?.pwd_length ?? defaultPasswordConfig.pwd_length,
      pwd_other: site?.pwd_other ?? defaultPasswordConfig.pwd_other,
    }

    if (key) {
      return config[key]
    }

    return config
  }
}
