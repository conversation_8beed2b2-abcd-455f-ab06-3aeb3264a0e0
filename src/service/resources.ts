import { Storages } from '@admin/utils'
import { ApiFileShow, ApiGisShow } from '@shencom/api'
import { type FileItem, type GisItem, ScResourceFileBase, ScResourceGisBase } from '@shencom/utils'

async function getFileInfo(ids: string[]) {
  if (!ids.length) {
    return []
  }
  const { data } = await ApiFileShow({ ids })
  return data
}

async function getGisInfo(ids: string[]) {
  if (!ids.length) {
    return []
  }
  const { data } = await ApiGisShow({ ids })
  return data
}

const Cache = {
  set: Storages.setLasting.bind(Storages),
  get: Storages.getLasting.bind(Storages),
  remove: Storages.removeLasting.bind(Storages),
}

const ResourcesFile = ScResourceFileBase({
  Cache,
  Fetch: getFileInfo,
})

const ResourcesGis = ScResourceGisBase({
  Cache,
  Fetch: getGisInfo,
})

const ServiceResources = {
  file: ResourcesFile,
  gis: ResourcesGis,
}

export { type FileItem, type GisItem, ServiceResources }
