import type { RouteLocationNormalized } from 'vue-router'
import { IsDev, Storages } from '@admin/utils'
import TdsServer from '@taptap/tds-msg/es/server'

const style
  = 'QGNoYXJzZXQgIlVURi04IjsKYm9keSB7YmFja2dyb3VuZDp3aGl0ZTt9CiNhcHAgLmFwcC1oZWFkZXIsCiNhcHAgLmFwcC10YWdzLAojYXBwIC5hcHAtZm9vdGVyLAojYXBwIC5hcHAtYXNpZGUge2Rpc3BsYXk6IG5vbmU7fQojYXBwIC5hcHAtd3JhcHBlciB7b3ZlcmZsb3cteTphdXRvIWltcG9ydGFudDt9CiNhcHAgLmFwcC13cmFwcGVyX3JpZ2h0IHttYXJnaW46IDA7bWluLWhlaWdodDoxMDAlfQ=='

const { protocol, hostname, port } = window.location

interface OldPlatformRouteTitle {
  fullPath: RouteLocationNormalized['fullPath']
  title: string
}

export class IframePlatformService {
  /** 旧平台自定义模块名 */
  static readonly routerName = 'iframe-middleware'

  static readonly host = IsDev ? `${protocol}//${hostname}:8080` : `${protocol}//${hostname}${port ? `:${port}` : ''}`

  static readonly style = style

  static handleUrl(path?: string) {
    let url = IsDev ? `${this.host}#` : `${this.host}/old/index.html#`

    url += path || ''

    return url
  }

  static clearStorage() {
    const iframe = document.createElement('iframe')
    const url = this.handleUrl('/iframe-logout')
    iframe.src = url
    iframe.style.display = 'none'
    iframe.id = 'iframe-logout'
    document.body.appendChild(iframe)

    const server = new TdsServer(this.host, () => document.querySelector('#iframe-logout'), {
      onMessage: (data) => {
        if (data.payload === 'clearStorage_success') {
          const logoutIframe = document.querySelector('#iframe-logout')
          if (logoutIframe) {
            document.body.removeChild(logoutIframe)
            server.destroy()
          }
        }
      },
    })
  }

  private static readonly _customTitleKey = 'oldPlatformRouteTitleList'

  static isGenerateRouteTitle = false

  static generateRouteTitle() {
    this.isGenerateRouteTitle = true
    return Storages.get<OldPlatformRouteTitle[]>(this._customTitleKey) || []
  }

  // 旧平台没有在菜单上的路由，需要手动记录一下标题
  static setRouteTitleList(data: OldPlatformRouteTitle[]) {
    Storages.set(this._customTitleKey, data)
  }
}
