import FloatingVue from 'floating-vue'

import VWave from 'v-wave'
import Message from 'vue-m-message'
import App from './App.vue'
import install from './install'

import { setupI18n } from './locales'

import router from './router'

import pinia from './store'

import ui from './ui-provider'
import 'floating-vue/dist/style.css'
import 'vue-m-message/dist/style.css'
import 'overlayscrollbars/overlayscrollbars.css'

// 自定义指令
import directive from '@/utils/directive'

// 错误日志上报
import errorLog from '@/utils/error.log'

// 加载 svg 图标
import 'virtual:svg-icons-register'

// 加载 iconify 图标
import { downloadAndInstall } from '@/iconify'
import icons from '@/iconify/index.json'

import 'virtual:uno.css'

// 全局样式
import '@/assets/styles/design-tokens'
import '@/assets/styles/globals.css'
import '@/assets/styles/element-ui.css'

const app = createApp(App)
app.use(FloatingVue, {
  distance: 12,
})
app.use(Message)
app.use(VWave, {})
app.use(pinia)
app.use(router)
app.use(ui)
app.use(setupI18n())
app.use(install)
directive(app)
errorLog(app)
if (icons.isOfflineUse) {
  for (const info of icons.collections) {
    downloadAndInstall(info)
  }
}

console.log('%c 当前环境：', 'font-size:13px;font-weight:bold;color:#409eff;', import.meta.env)

app.mount('#app')
