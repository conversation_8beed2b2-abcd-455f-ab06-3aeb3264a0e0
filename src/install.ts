import type { App } from 'vue'
import { api, axios } from '@/api'
import * as service from '@/service'
import * as utils from '@/utils'
import { init } from '@shencom/api'

window.$global = window.$global || {}
window.$global.$api = api
window.$global.$utils = utils
window.$global.$http = axios
window.$global.$service = service

export default function install(app: App) {
  init(axios, '')

  Object.assign(app.config.globalProperties, window.$global)
}
