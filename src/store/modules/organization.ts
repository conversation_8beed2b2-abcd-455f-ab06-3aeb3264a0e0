import { ApiSwitchOrganization, ServiceUser } from '@/service'
import { Storages } from '@/utils'
import axios from 'axios'

interface Organization {
  id: string
  name: string
}

export function getCacheOrganizationId() {
  return Storages.getUser<string>('organization_id') || ''
}

const useOrganizationStore = defineStore(
  // 唯一ID
  'organization',
  () => {
    const organization = ref<Organization[]>([])

    const currentOrganization = ref<Organization>()

    function setCacheOrganizationId(id: string) {
      currentOrganization.value = organization.value.find(item => item.id === id)

      // 设置请求头
      axios.defaults.headers.common.organizationId = id
      Storages.setUser('organization_id', id, 60)
      // 切换组织
      ApiSwitchOrganization(id)
    }

    async function getOrganization() {
      const data = await ServiceUser.requestOrganization()
      organization.value = data
      const id = getCacheOrganizationId()

      if (id) {
        setCacheOrganizationId(id)
      }
      else {
        const switchData = await ApiSwitchOrganization()
        if (switchData && switchData.id) {
          setCacheOrganizationId(switchData.id)
        }
      }

      if (!currentOrganization.value && data.length > 0) {
        setCacheOrganizationId(data[0].id)
      }
    }

    return {
      organization,
      currentOrganization,
      getOrganization,
      setCacheOrganizationId,
    }
  },
)

export default useOrganizationStore
