import type { Settings } from '#/global'
import apiUser from '@/api/modules/user'
import router from '@/router'
import { ServiceUser } from '@/service'
import { IframePlatformService } from '@/service/iframe-platform'
import settingsDefault from '@/settings'
import { Storages, UserInfo } from '@admin/utils'
import { createDefu } from 'defu'
import { cloneDeep } from 'lodash-es'
import useMenuStore from './menu'
import useRouteStore from './route'
import useSettingsStore from './settings'
import useTabbarStore from './tabbar'

const useUserStore = defineStore(
  // 唯一ID
  'user',
  () => {
    const settingsStore = useSettingsStore()
    const tabbarStore = useTabbarStore()
    const routeStore = useRouteStore()
    const menuStore = useMenuStore()

    const account = ref(UserInfo.getPhone() || UserInfo.getUid() || '')
    const userName = ref(UserInfo.getRealname() || UserInfo.getNickname() || '')
    // const token = ref(UserInfo.getToken() || '')
    const avatar = ref(UserInfo.getAvatar() || '')
    const permissions = ref<string[]>([])

    function isLogin() {
      return UserInfo.isLogin()
    }

    // const isLogin = computed(() => {
    //   if (token.value) {
    //     return true
    //   }
    //   return false
    // })

    // 登录
    async function login() {
      const user = UserInfo.getUserInfo()
      if (!user) {
        return
      }
      account.value = UserInfo.getPhone() || UserInfo.getNickname() || ''
      avatar.value = UserInfo.getAvatar() || ''
      userName.value = UserInfo.getRealname() || UserInfo.getNickname() || ''
    }
    // 登出
    async function logout(redirect = router.currentRoute.value.fullPath) {
      tabbarStore.clean()
      routeStore.removeRoutes()
      menuStore.setActived(0)
      Storages.clear()
      IframePlatformService.clearStorage()
      // storage.local.remove('account')
      // storage.local.remove('token')
      // storage.local.remove('avatar')
      account.value = ''
      avatar.value = ''
      userName.value = ''
      permissions.value = []
      router.replace({
        name: 'login',
        query: {
          ...(redirect !== settingsStore.settings.home.fullPath && router.currentRoute.value.name !== 'login' && { redirect }),
        },
      })
    }
    // 获取权限
    async function getPermissions() {
      const list = await ServiceUser.requestPermissions()
      permissions.value = list
      // permissions.value = ['permission.browse', 'permission.create', 'permission.edit', 'permission.remove']
    }

    // 修改密码
    async function editPassword(data: {
      password: string
      newpassword: string
    }) {
      await apiUser.passwordEdit(data)
    }

    // 框架已将可提供给用户配置的选项提取出来，请勿新增其他选项，不需要的选项可以在这里注释掉
    const preferences = ref<Settings.all>({
      app: {
        colorScheme: settingsDefault.app.colorScheme,
        // lightTheme: settingsDefault.app.lightTheme,
        // darkTheme: settingsDefault.app.darkTheme,
        enableColorAmblyopiaMode: settingsDefault.app.enableColorAmblyopiaMode,
        enableProgress: settingsDefault.app.enableProgress,
        defaultLang: settingsDefault.app.defaultLang,
      },
      menu: {
        mode: settingsDefault.menu.mode,
        style: settingsDefault.menu.style,
        isRounded: settingsDefault.menu.isRounded,
        // switchMainMenuAndPageJump: settingsDefault.menu.switchMainMenuAndPageJump,
        // subMenuUniqueOpened: settingsDefault.menu.subMenuUniqueOpened,
        // subMenuCollapse: settingsDefault.menu.subMenuCollapse,
        // subMenuAutoCollapse: settingsDefault.menu.subMenuAutoCollapse,
        // enableSubMenuCollapseButton: settingsDefault.menu.enableSubMenuCollapseButton,
      },
      layout: {
        widthMode: settingsDefault.layout.widthMode,
      },
      mainPage: {
        enableTransition: settingsDefault.mainPage.enableTransition,
        transitionMode: settingsDefault.mainPage.transitionMode,
      },
      topbar: {
        mode: settingsDefault.topbar.mode,
        switchTabbarAndToolbar: settingsDefault.topbar.switchTabbarAndToolbar,
      },
      tabbar: {
        style: settingsDefault.tabbar.style,
        enableIcon: settingsDefault.tabbar.enableIcon,
        dblclickAction: settingsDefault.tabbar.dblclickAction,
        enableMemory: settingsDefault.tabbar.enableMemory,
      },
      toolbar: {
        breadcrumb: settingsDefault.toolbar.breadcrumb,
        navSearch: settingsDefault.toolbar.navSearch,
        fullscreen: settingsDefault.toolbar.fullscreen,
        pageReload: settingsDefault.toolbar.pageReload,
        colorScheme: settingsDefault.toolbar.colorScheme,
        layout: settingsDefault.toolbar.layout,
      },
      breadcrumb: {
        style: settingsDefault.breadcrumb.style,
        enableMainMenu: settingsDefault.breadcrumb.enableMainMenu,
      },
    })
    // 此处没有使用 lodash 的 defaultsDeep 函数，而是基于 defu 库自定义了一个函数，只合并 settings 中有的属性，而不是全部合并，这样做的目的是为了排除用户历史遗留的偏好配置
    const customDefaultsDeep = createDefu((obj, key, value) => {
      if (obj[key] === undefined) {
        delete obj[key]
        return true
      }
      if (Array.isArray(obj[key]) && Array.isArray(value)) {
        obj[key] = value
        return true
      }
    })
    // isPreferencesUpdating 用于防止循环更新
    let isPreferencesUpdating = false
    watch(preferences, (val) => {
      if (!settingsStore.settings.userPreferences.enable) {
        return
      }
      if (!isPreferencesUpdating) {
        isPreferencesUpdating = true
        settingsStore.updateSettings(cloneDeep(val))
      }
      else {
        isPreferencesUpdating = false
      }
      updatePreferences(cloneDeep(val))
    }, {
      deep: true,
    })
    watch(() => settingsStore.settings, (val) => {
      if (!settingsStore.settings.userPreferences.enable) {
        return
      }
      if (!isPreferencesUpdating) {
        isPreferencesUpdating = true
        preferences.value = customDefaultsDeep(val, preferences.value)
      }
      else {
        isPreferencesUpdating = false
      }
    }, {
      deep: true,
    })
    // isPreferencesInited 用于防止初始化时触发更新
    let isPreferencesInited = false
    // 获取偏好设置
    async function getPreferences() {
      let data: Settings.all = {}
      if (settingsStore.settings.userPreferences.storageTo === 'local') {
        if (Storages.get('userPreferences')) {
          data = Storages.get('userPreferences')[account.value] || {}
        }
      }
      else if (settingsStore.settings.userPreferences.storageTo === 'server') {
        const res = await apiUser.preferences()
        data = JSON.parse(res.data.preferences || '{}') as Settings.all
      }
      preferences.value = customDefaultsDeep(data, preferences.value)
    }
    // 更新偏好设置
    async function updatePreferences(data: Settings.all = {}) {
      if (!isPreferencesInited) {
        isPreferencesInited = true
        return
      }
      if (settingsStore.settings.userPreferences.storageTo === 'local') {
        const userPreferencesData = Storages.get('userPreferences') || {}
        userPreferencesData[account.value] = data
        Storages.set('userPreferences', userPreferencesData)
      }
      else if (settingsStore.settings.userPreferences.storageTo === 'server') {
        await apiUser.preferencesEdit(JSON.stringify(data))
      }
    }

    return {
      account,
      userName,
      // token,
      avatar,
      permissions,
      isLogin,
      login,
      logout,
      getPermissions,
      editPassword,
      preferences,
      getPreferences,
      updatePreferences,
    }
  },
)

export default useUserStore
