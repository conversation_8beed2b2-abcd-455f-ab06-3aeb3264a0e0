<script setup lang="ts">
import { ServiceSite } from '@/service'
import useSettingsStore from '@/store/modules/settings'

defineOptions({
  name: 'Copyright',
})

const route = useRoute()
const settingsStore = useSettingsStore()

const company = computed(() => {
  if (ServiceSite.config('mng_config_copyright')) {
    return ServiceSite.config('mng_config_copyright')
  }
  return settingsStore.settings.copyright.company
})

const website = computed(() => {
  if (ServiceSite.config('mng_config_info')) {
    return ServiceSite.config('mng_config_info')
  }
  return settingsStore.settings.copyright.website
})
</script>

<template>
  <footer v-if="route.meta.copyright ?? settingsStore.settings.copyright.enable" class="copyright">
    <span>Copyright</span>
    <SvgIcon name="i-ri:copyright-line" :size="18" />
    <span v-if="settingsStore.settings.copyright.dates">{{ settingsStore.settings.copyright.dates }}</span>
    <template v-if="company">
      <a v-if="website" :href="website" target="_blank" rel="noopener">{{ company }}</a>
      <span v-else>{{ company }}</span>
    </template>
    <a v-if="settingsStore.settings.copyright.beian" href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">{{ settingsStore.settings.copyright.beian }}</a>
  </footer>
</template>

<style scoped>
.copyright {
  --uno: flex items-center justify-center flex-wrap my-4 px-4 text-sm text-stone-5;

  span,
  a {
    --uno: px-1;
  }

  a {
    --uno: text-center no-underline text-stone-5 hover-text-dark dark-hover-text-light transition;
  }
}
</style>
