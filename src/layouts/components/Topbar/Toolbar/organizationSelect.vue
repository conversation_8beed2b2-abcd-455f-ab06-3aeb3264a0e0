<script setup lang="ts">
import { ServiceSite, ServiceUser } from '@/service'
import useOrganizationStore from '@/store/modules/organization'
import { ArrowDown } from '@element-plus/icons-vue'

const organizationStore = useOrganizationStore()

const organization = computed(() => organizationStore.organization)

async function refreshPermission() {
  ServiceUser.removeCacheMenus()
  ServiceUser.removeCachePermissions()
  ServiceUser.removeCacheOrganization()
  ServiceSite.removeCache()
  window.location.reload()
}

function onDropdownCommand(command: string) {
  if (command !== organizationStore.currentOrganization?.id) {
    organizationStore.setCacheOrganizationId(command)
    refreshPermission()
  }
}
</script>

<template>
  <el-dropdown v-if="organizationStore.currentOrganization" trigger="click" class="mr-3" @command="onDropdownCommand">
    <div class="flex items-center gap-1 text-4 text-white">
      {{ organizationStore.currentOrganization?.name }}
      <el-icon size="18">
        <ArrowDown />
      </el-icon>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item v-for="item in organization" :key="item.id" :command="item.id" class="!px-5 !text-4">
          {{ item.name }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
