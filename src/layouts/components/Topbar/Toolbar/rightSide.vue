<script setup lang="ts">
import { ServiceSite } from '@/service/site'
import { ServiceUser } from '@/service/user'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import eventBus from '@/utils/eventBus'
import { useI18n } from 'vue-i18n'
import OrganizationSelect from './organizationSelect.vue'
import Tools from './tools.vue'

defineOptions({
  name: 'ToolbarRightSide',
})

const router = useRouter()

const settingsStore = useSettingsStore()
const userStore = useUserStore()

const { t } = useI18n()
const { generateI18nTitle } = useMenu()

const avatarError = ref(false)
watch(() => userStore.avatar, () => {
  if (avatarError.value) {
    avatarError.value = false
  }
})

async function refreshPermission() {
  ServiceUser.removeCacheMenus()
  ServiceUser.removeCachePermissions()
  ServiceUser.removeCacheOrganization()
  ServiceSite.removeCache()
  window.location.reload()
}
</script>

<template>
  <div class="flex items-center">
    <OrganizationSelect />
    <Tools mode="right-side" />

    <HDropdownMenu
      :items="[
        [
          { label: generateI18nTitle(settingsStore.settings.home.title), handle: () => router.push({ path: settingsStore.settings.home.fullPath }), hide: !settingsStore.settings.home.enable },
          { label: t('app.preferences'), handle: () => eventBus.emit('global-preferences-toggle'), hide: !settingsStore.settings.userPreferences.enable },
          { label: '刷新权限', handle: refreshPermission },
        ],
        [
          { label: t('app.hotkeys'), handle: () => eventBus.emit('global-hotkeys-intro-toggle'), hide: settingsStore.mode !== 'pc' },
        ],
        [
          { label: t('app.logout'), handle: () => userStore.logout(settingsStore.settings.home.fullPath) },
        ],
      ]" class="flex-center cursor-pointer px-2"
    >
      <div class="flex-center gap-1">
        <img v-if="userStore.avatar && !avatarError" :src="userStore.avatar" :onerror="() => (avatarError = true)" class="h-[24px] w-[24px] rounded-full">
        <SvgIcon v-else name="i-carbon:user-avatar-filled-alt" :size="24" class="text-gray-400" />
        {{ userStore.userName }}
        <SvgIcon name="i-ep:caret-bottom" />
      </div>
    </HDropdownMenu>
  </div>
</template>
