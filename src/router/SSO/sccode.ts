import { UserInfo } from '@/utils'
import { ApiScCodeLogin } from '@shencom/api'
import { UrlParam } from '@shencom/utils'

function clearUrlHref() {
  const { origin, hash, pathname } = window.location
  window.history.pushState({}, '', `${origin}${pathname}${hash}`)
}

export class ScCode {
  static has() {
    return UrlParam('sccode')
  }

  static async login() {
    const token = UserInfo.getToken()
    if (token) {
      return
    }

    const sccode = UrlParam('sccode')
    if (sccode) {
      const { data } = await ApiScCodeLogin(sccode)
      clearUrlHref()
      if (data) {
        UserInfo.setRootInfo(data)
      }
    }
  }
}
