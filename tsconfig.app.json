{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["ESNext", "DOM", "DOM.Iterable"], "moduleDetection": "force", "useDefineForClassFields": true, "baseUrl": "./", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"@/*": ["src/*"], "#/*": ["src/types/*"]}, "resolveJsonModule": true, "types": ["vite/client", "vite-plugin-pages/client", "vite-plugin-vue-meta-layouts/client", "@intlify/unplugin-vue-i18n/messages", "vite-plugin-app-loading/client", "element-plus/global"], "allowImportingTsExtensions": true, "allowJs": false, "strict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "noEmit": true, "sourceMap": true, "esModuleInterop": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "apps/**/*.ts", "apps/**/*.d.ts", "apps/**/*.tsx", "apps/**/*.vue", "src/views/vue2-index"]}